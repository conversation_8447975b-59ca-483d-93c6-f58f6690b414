use std::time::Duration;
use tokio::time::timeout;
use serde_json::json;

use secure_password_daemon::native_messaging::{
    error::{NativeMessagingError, Result},
    protocol::{NativeMessage, ProtocolCodec, ProtocolVersion, MessageType},
    host::{HostConfig, NativeMessagingHost, HostState},
    proxy::{ProxyConfig, RequestProxy},
    browser::{BrowserType, BrowserRegistry, ChromeAdapter, FirefoxAdapter},
    handlers::{<PERSON>lerRegistry, DefaultMessageHandler},
    registry::{RegistryManager, HostRegistration},
};
use secure_password_daemon::ipc::{IpcClient, ClientConfig as IpcClientConfig};

/// 测试协议编解码器
#[tokio::test]
async fn test_protocol_codec() {
    let codec = ProtocolCodec::new(ProtocolVersion::V1);
    
    // 测试请求消息编解码
    let request = NativeMessage::new_request(
        "test-request".to_string(),
        json!({"action": "get_password", "domain": "example.com"}),
        "test-extension".to_string(),
    );
    
    let encoded = codec.encode(&request).expect("编码失败");
    let decoded = codec.decode(&encoded).expect("解码失败");
    
    assert_eq!(request.request_id, decoded.request_id);
    assert_eq!(request.message_type, decoded.message_type);
    assert_eq!(request.payload, decoded.payload);
    assert_eq!(request.source, decoded.source);
}

/// 测试Native Message创建和验证
#[tokio::test]
async fn test_native_message_creation() {
    // 测试请求消息
    let request = NativeMessage::new_request(
        "test-id".to_string(),
        json!({"action": "test"}),
        "test-extension".to_string(),
    );
    
    assert_eq!(request.get_message_type(), MessageType::Request);
    assert!(request.validate().is_ok());
    
    // 测试响应消息
    let response = NativeMessage::new_response(
        "test-id".to_string(),
        json!({"result": "success"}),
        "host".to_string(),
    );
    
    assert_eq!(response.get_message_type(), MessageType::Response);
    assert!(response.validate().is_ok());
    
    // 测试错误消息
    let error = NativeMessage::new_error(
        "test-id".to_string(),
        "test_error".to_string(),
        "测试错误".to_string(),
        "host".to_string(),
    );
    
    assert_eq!(error.get_message_type(), MessageType::Error);
    assert!(error.validate().is_ok());
    
    // 测试Ping消息
    let ping = NativeMessage::new_ping("test-extension".to_string());
    assert_eq!(ping.get_message_type(), MessageType::Ping);
    assert!(ping.validate().is_ok());
}

/// 测试Host配置
#[tokio::test]
async fn test_host_config() {
    let config = HostConfig::default();
    
    assert_eq!(config.host_name, "secure_password_host");
    assert_eq!(config.protocol_version, ProtocolVersion::V1);
    assert_eq!(config.request_timeout, Duration::from_secs(30));
    assert_eq!(config.max_concurrent_requests, 10);
    assert!(!config.debug_mode);
    assert!(!config.verify_signatures);
}

/// 测试Host创建和状态管理
#[tokio::test]
async fn test_host_creation_and_state() {
    let config = HostConfig::default();
    let host = NativeMessagingHost::new(config).await.expect("创建Host失败");
    
    // 测试初始状态
    assert_eq!(host.get_state().await, HostState::Stopped);
    
    // 测试初始统计信息
    let stats = host.get_stats().await;
    assert_eq!(stats.total_requests, 0);
    assert_eq!(stats.successful_requests, 0);
    assert_eq!(stats.failed_requests, 0);
    assert_eq!(stats.concurrent_requests, 0);
    assert!(stats.started_at.is_none());
    assert!(!stats.ipc_connected);
}

/// 测试消息验证
#[tokio::test]
async fn test_message_validation() {
    let config = HostConfig::default();
    let host = NativeMessagingHost::new(config).await.expect("创建Host失败");
    
    // 测试有效消息
    let valid_message = NativeMessage::new_request(
        "valid-id".to_string(),
        json!({"action": "test"}),
        "valid-extension".to_string(),
    );
    
    assert!(host.validate_browser_message(&valid_message).await.is_ok());
    
    // 测试无效消息（空请求ID）
    let mut invalid_message = valid_message.clone();
    invalid_message.request_id = "".to_string();
    
    assert!(host.validate_browser_message(&invalid_message).await.is_err());
}

/// 测试Ping消息处理
#[tokio::test]
async fn test_ping_message_handling() {
    let config = HostConfig::default();
    let host = NativeMessagingHost::new(config).await.expect("创建Host失败");
    
    let ping_message = NativeMessage::new_ping("test-extension".to_string());
    let response = host.handle_ping_message(&ping_message).await.expect("处理Ping失败");
    
    assert_eq!(response.get_message_type(), MessageType::Pong);
    assert_eq!(response.request_id, ping_message.request_id);
    assert_eq!(response.source, "host");
}

/// 测试浏览器适配器
#[tokio::test]
async fn test_browser_adapters() {
    // 测试Chrome适配器
    let chrome_adapter = ChromeAdapter::new();
    assert_eq!(chrome_adapter.browser_type(), BrowserType::Chrome);
    
    // 测试Firefox适配器
    let firefox_adapter = FirefoxAdapter::new();
    assert_eq!(firefox_adapter.browser_type(), BrowserType::Firefox);
}

/// 测试浏览器注册表
#[tokio::test]
async fn test_browser_registry() {
    let registry = BrowserRegistry::new();
    
    // 测试支持的浏览器
    let supported = registry.supported_browsers();
    assert!(supported.contains(&BrowserType::Chrome));
    assert!(supported.contains(&BrowserType::Firefox));
    assert!(supported.contains(&BrowserType::Edge));
    
    // 测试获取适配器
    assert!(registry.get_adapter(&BrowserType::Chrome).is_some());
    assert!(registry.get_adapter(&BrowserType::Firefox).is_some());
}

/// 测试消息处理器注册表
#[tokio::test]
async fn test_handler_registry() {
    let registry = HandlerRegistry::new();
    
    // 测试注册处理器
    let handler = std::sync::Arc::new(DefaultMessageHandler::new("test-handler".to_string()));
    registry.register_handler("test".to_string(), handler.clone()).await;
    
    // 测试获取处理器
    let retrieved = registry.get_handler("test").await;
    assert!(retrieved.is_some());
    
    // 测试获取已注册类型
    let types = registry.get_registered_types().await;
    assert!(types.contains(&"test".to_string()));
}

/// 测试主机注册
#[tokio::test]
async fn test_host_registration() {
    let registration = HostRegistration::new(
        "test-host".to_string(),
        "Test Host".to_string(),
        "/path/to/test-host".to_string(),
    );
    
    assert_eq!(registration.name, "test-host");
    assert_eq!(registration.description, "Test Host");
    assert_eq!(registration.path, "/path/to/test-host");
    assert_eq!(registration.type_field, "stdio");
    assert!(registration.allowed_origins.is_empty());
    assert!(registration.allowed_extensions.is_empty());
}

/// 测试注册管理器
#[tokio::test]
async fn test_registry_manager() {
    let manager = RegistryManager::new("/path/to/daemon".to_string());
    
    // 测试获取注册信息
    let chrome_registration = manager.get_registration(&BrowserType::Chrome);
    assert!(chrome_registration.is_some());
    
    let firefox_registration = manager.get_registration(&BrowserType::Firefox);
    assert!(firefox_registration.is_some());
    
    // 测试获取所有注册信息
    let all_registrations = manager.get_all_registrations();
    assert!(all_registrations.contains_key(&BrowserType::Chrome));
    assert!(all_registrations.contains_key(&BrowserType::Firefox));
}

/// 测试错误处理
#[tokio::test]
async fn test_error_handling() {
    // 测试错误创建
    let io_error = NativeMessagingError::IoError("测试IO错误".to_string());
    assert_eq!(io_error.to_string(), "IO错误: 测试IO错误");
    
    let protocol_error = NativeMessagingError::ProtocolError("测试协议错误".to_string());
    assert_eq!(protocol_error.to_string(), "协议错误: 测试协议错误");
    
    let security_error = NativeMessagingError::SecurityError("测试安全错误".to_string());
    assert_eq!(security_error.to_string(), "安全错误: 测试安全错误");
    
    // 测试错误转换
    let std_error = std::io::Error::new(std::io::ErrorKind::NotFound, "文件未找到");
    let native_error: NativeMessagingError = std_error.into();
    
    match native_error {
        NativeMessagingError::IoError(_) => {}, // 预期的转换
        _ => panic!("错误转换失败"),
    }
}

/// 测试协议版本
#[tokio::test]
async fn test_protocol_versions() {
    // 测试版本转换
    assert_eq!(ProtocolVersion::V1.to_u32(), 1);
    assert_eq!(ProtocolVersion::V2.to_u32(), 2);
    
    // 测试版本解析
    assert_eq!(ProtocolVersion::from_u32(1), Some(ProtocolVersion::V1));
    assert_eq!(ProtocolVersion::from_u32(2), Some(ProtocolVersion::V2));
    assert_eq!(ProtocolVersion::from_u32(99), None);
}

/// 测试消息类型
#[tokio::test]
async fn test_message_types() {
    let request = NativeMessage::new_request(
        "test".to_string(),
        json!({}),
        "test".to_string(),
    );
    assert_eq!(request.get_message_type(), MessageType::Request);
    
    let response = NativeMessage::new_response(
        "test".to_string(),
        json!({}),
        "test".to_string(),
    );
    assert_eq!(response.get_message_type(), MessageType::Response);
    
    let error = NativeMessage::new_error(
        "test".to_string(),
        "error".to_string(),
        "message".to_string(),
        "test".to_string(),
    );
    assert_eq!(error.get_message_type(), MessageType::Error);
    
    let ping = NativeMessage::new_ping("test".to_string());
    assert_eq!(ping.get_message_type(), MessageType::Ping);
}

/// 测试消息扩展
#[tokio::test]
async fn test_message_extensions() {
    let mut message = NativeMessage::new_request(
        "test".to_string(),
        json!({}),
        "test".to_string(),
    );
    
    // 测试添加扩展
    message.add_extension("test_key".to_string(), json!("test_value"));
    
    // 测试获取扩展
    let extension = message.get_extension("test_key");
    assert!(extension.is_some());
    assert_eq!(extension.unwrap(), &json!("test_value"));
    
    // 测试不存在的扩展
    let missing = message.get_extension("missing_key");
    assert!(missing.is_none());
}

/// 测试超时处理
#[tokio::test]
async fn test_timeout_handling() {
    // 测试超时配置
    let mut config = HostConfig::default();
    config.request_timeout = Duration::from_millis(100);
    
    let host = NativeMessagingHost::new(config).await.expect("创建Host失败");
    
    // 验证超时配置
    let stats = host.get_stats().await;
    assert_eq!(stats.total_requests, 0);
}

/// 测试并发限制
#[tokio::test]
async fn test_concurrency_limits() {
    let mut config = HostConfig::default();
    config.max_concurrent_requests = 5;
    
    let host = NativeMessagingHost::new(config).await.expect("创建Host失败");
    
    // 测试并发请求统计
    let stats = host.get_stats().await;
    assert_eq!(stats.concurrent_requests, 0);
}

/// 测试统计信息更新
#[tokio::test]
async fn test_stats_updates() {
    let config = HostConfig::default();
    let host = NativeMessagingHost::new(config).await.expect("创建Host失败");
    
    // 测试统计信息更新
    host.increment_total_requests().await;
    host.increment_successful_requests().await;
    host.increment_concurrent_requests().await;
    
    let stats = host.get_stats().await;
    assert_eq!(stats.total_requests, 1);
    assert_eq!(stats.successful_requests, 1);
    assert_eq!(stats.concurrent_requests, 1);
    
    // 测试并发请求减少
    host.decrement_concurrent_requests().await;
    let stats = host.get_stats().await;
    assert_eq!(stats.concurrent_requests, 0);
}

/// 测试响应时间统计
#[tokio::test]
async fn test_response_time_stats() {
    let config = HostConfig::default();
    let host = NativeMessagingHost::new(config).await.expect("创建Host失败");
    
    // 测试响应时间更新
    let response_time = Duration::from_millis(100);
    host.update_response_time_stats(response_time).await;
    
    let stats = host.get_stats().await;
    assert_eq!(stats.avg_response_time_ms, 100.0);
    
    // 测试移动平均
    let response_time2 = Duration::from_millis(200);
    host.update_response_time_stats(response_time2).await;
    
    let stats = host.get_stats().await;
    // 移动平均：(100 * 0.9) + (200 * 0.1) = 90 + 20 = 110
    assert_eq!(stats.avg_response_time_ms, 110.0);
}

/// 测试最后活动时间更新
#[tokio::test]
async fn test_last_activity_update() {
    let config = HostConfig::default();
    let host = NativeMessagingHost::new(config).await.expect("创建Host失败");
    
    // 初始状态
    let stats = host.get_stats().await;
    assert!(stats.last_activity.is_none());
    
    // 更新最后活动时间
    host.update_last_activity().await;
    
    let stats = host.get_stats().await;
    assert!(stats.last_activity.is_some());
}

/// 性能测试：大量消息处理
#[tokio::test]
async fn test_message_processing_performance() {
    let codec = ProtocolCodec::new(ProtocolVersion::V1);
    
    let start_time = std::time::Instant::now();
    
    // 处理1000个消息
    for i in 0..1000 {
        let message = NativeMessage::new_request(
            format!("request-{}", i),
            json!({"action": "test", "data": i}),
            "test-extension".to_string(),
        );
        
        let encoded = codec.encode(&message).expect("编码失败");
        let decoded = codec.decode(&encoded).expect("解码失败");
        
        assert_eq!(message.request_id, decoded.request_id);
    }
    
    let elapsed = start_time.elapsed();
    println!("处理1000个消息用时: {:?}", elapsed);
    
    // 确保性能合理（应该在1秒内完成）
    assert!(elapsed < Duration::from_secs(1));
}

/// 内存使用测试
#[tokio::test]
async fn test_memory_usage() {
    let config = HostConfig::default();
    let host = NativeMessagingHost::new(config).await.expect("创建Host失败");
    
    // 创建多个消息但不持有引用
    for i in 0..100 {
        let message = NativeMessage::new_request(
            format!("request-{}", i),
            json!({"action": "test", "data": i}),
            "test-extension".to_string(),
        );
        
        // 验证消息创建成功
        assert!(message.validate().is_ok());
    }
    
    // 验证Host状态正常
    assert_eq!(host.get_state().await, HostState::Stopped);
}

/// 边界条件测试
#[tokio::test]
async fn test_edge_cases() {
    // 测试空消息处理
    let empty_message = NativeMessage::new_request(
        "empty".to_string(),
        json!({}),
        "test".to_string(),
    );
    assert!(empty_message.validate().is_ok());
    
    // 测试长消息处理
    let long_data = "x".repeat(1000);
    let long_message = NativeMessage::new_request(
        "long".to_string(),
        json!({"data": long_data}),
        "test".to_string(),
    );
    assert!(long_message.validate().is_ok());
    
    // 测试特殊字符处理
    let special_message = NativeMessage::new_request(
        "special".to_string(),
        json!({"data": "测试中文🚀"}),
        "test".to_string(),
    );
    assert!(special_message.validate().is_ok());
} 