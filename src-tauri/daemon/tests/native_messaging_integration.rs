use std::time::Duration;
use std::collections::HashMap;
use serde_json::json;
use tokio::time::timeout;

use secure_password_daemon::native_messaging::*;
use secure_password_daemon::ipc::{IpcClient, ClientConfig};

/// 集成测试辅助函数
mod test_helpers {
    use super::*;
    
    /// 创建测试用的主机配置
    pub fn create_test_host_config() -> HostConfig {
        HostConfig::default()
    }
    
    /// 创建测试用的代理配置
    pub fn create_test_proxy_config() -> ProxyConfig {
        ProxyConfig::default()
    }
    
    /// 创建测试用的IPC客户端
    pub fn create_test_ipc_client() -> IpcClient {
        let config = ClientConfig::default();
        IpcClient::new(config)
    }
    
    /// 创建测试用的Native Message
    pub fn create_test_request_message() -> NativeMessage {
        NativeMessage::new_request(
            "test_request_001".to_string(),
            json!({
                "action": "get_credentials",
                "domain": "example.com",
                "username": "testuser"
            }),
            "test_extension_id".to_string(),
        )
    }
    
    /// 创建测试用的Ping消息
    pub fn create_test_ping_message() -> NativeMessage {
        NativeMessage::new_ping(
            "ping_001".to_string(),
            "test_extension_id".to_string(),
        )
    }
    
    /// 验证响应消息
    pub fn verify_response_message(response: &NativeMessage, expected_request_id: &str) -> bool {
        response.request_id == expected_request_id &&
        response.get_message_type() == MessageType::Response &&
        response.source == "daemon"
    }
    
    /// 验证错误消息
    pub fn verify_error_message(response: &NativeMessage, expected_request_id: &str) -> bool {
        response.request_id == expected_request_id &&
        response.get_message_type() == MessageType::Error &&
        response.source == "daemon"
    }
}

/// 测试协议编解码功能
#[tokio::test]
async fn test_protocol_codec_integration() {
    // 创建编解码器
    let codec = protocol::ProtocolCodec::new(protocol::ProtocolVersion::V2);
    
    // 创建测试消息
    let original_message = test_helpers::create_test_request_message();
    
    // 编码消息
    let encoded = codec.encode(&original_message).expect("编码失败");
    assert!(!encoded.is_empty(), "编码结果不应为空");
    
    // 解码消息
    let decoded_message = codec.decode(&encoded).expect("解码失败");
    
    // 验证解码结果
    assert_eq!(decoded_message.request_id, original_message.request_id);
    assert_eq!(decoded_message.message_type, original_message.message_type);
    assert_eq!(decoded_message.payload, original_message.payload);
    assert_eq!(decoded_message.source, original_message.source);
}

/// 测试协议版本协商
#[tokio::test]
async fn test_protocol_negotiation() {
    let negotiator = protocol::ProtocolNegotiator::new();
    
    // 测试支持的版本
    let supported_versions = negotiator.get_supported_versions();
    assert!(!supported_versions.is_empty(), "应该支持至少一个版本");
    
    // 测试版本协商
    let client_versions = vec![protocol::ProtocolVersion::V1, protocol::ProtocolVersion::V2];
    let negotiated_version = negotiator.negotiate(&client_versions);
    assert!(negotiated_version.is_some(), "应该能够协商出版本");
    
    // 测试不兼容的版本
    let incompatible_versions = vec![];
    let no_version = negotiator.negotiate(&incompatible_versions);
    assert!(no_version.is_none(), "不兼容的版本应该返回None");
}

/// 测试请求代理功能
#[tokio::test]
async fn test_request_proxy_integration() {
    let proxy_config = test_helpers::create_test_proxy_config();
    let ipc_client = test_helpers::create_test_ipc_client();
    
    // 创建请求代理
    let proxy = RequestProxy::new(proxy_config, ipc_client).await.expect("创建代理失败");
    
    // 获取初始统计信息
    let initial_stats = proxy.get_stats().await;
    assert_eq!(initial_stats.total_requests, 0);
    
    // 注意：由于这是集成测试，我们不能真正发送请求到IPC服务器
    // 这里我们主要测试代理的创建和配置
    let config = proxy.get_config();
    assert_eq!(config.timeout, Duration::from_secs(30));
}

/// 测试消息验证功能
#[tokio::test]
async fn test_message_validation() {
    let mut message = test_helpers::create_test_request_message();
    
    // 测试有效消息
    assert!(message.validate().is_ok(), "有效消息应该通过验证");
    
    // 测试无效消息 - 空请求ID
    message.request_id = String::new();
    assert!(message.validate().is_err(), "空请求ID应该验证失败");
    
    // 测试无效消息 - 空来源
    message.request_id = "test_id".to_string();
    message.source = String::new();
    assert!(message.validate().is_err(), "空来源应该验证失败");
    
    // 测试消息大小限制
    let mut large_message = test_helpers::create_test_request_message();
    large_message.payload = json!({
        "large_data": "x".repeat(2 * 1024 * 1024) // 2MB数据
    });
    
    let size_check_result = large_message.check_size_limit(1024 * 1024); // 1MB限制
    assert!(size_check_result.is_err(), "超大消息应该被拒绝");
}

/// 测试浏览器适配器功能
#[tokio::test]
async fn test_browser_adapter_integration() {
    // 测试Chrome适配器
    let chrome_config = browser::BrowserAdapterConfig::default();
    let chrome_adapter = browser::ChromeAdapter::new(chrome_config);
    assert_eq!(chrome_adapter.browser_type(), browser::BrowserType::Chrome);
    
    // 测试Firefox适配器
    let firefox_config = browser::BrowserAdapterConfig {
        browser_type: browser::BrowserType::Firefox,
        ..Default::default()
    };
    let firefox_adapter = browser::FirefoxAdapter::new(firefox_config);
    assert_eq!(firefox_adapter.browser_type(), browser::BrowserType::Firefox);
    
    // 测试浏览器注册表
    let mut registry = browser::BrowserRegistry::new();
    registry.register_adapter(Box::new(chrome_adapter));
    registry.register_adapter(Box::new(firefox_adapter));
    
    // 验证适配器注册
    assert!(registry.get_adapter(&browser::BrowserType::Chrome).is_some());
    assert!(registry.get_adapter(&browser::BrowserType::Firefox).is_some());
    assert!(registry.get_adapter(&browser::BrowserType::Safari).is_none());
}

/// 测试消息处理器功能
#[tokio::test]
async fn test_message_handler_integration() {
    let mut handler_registry = handlers::HandlerRegistry::new();
    let default_handler = handlers::DefaultMessageHandler::new();
    
    // 注册默认处理器
    handler_registry.register_handler("default".to_string(), Box::new(default_handler));
    
    // 测试处理器查找
    let handler = handler_registry.get_handler("default").await;
    assert!(handler.is_some(), "应该能找到注册的处理器");
    
    // 测试消息类型支持
    let found_handler = handler_registry.get_handler("request").await;
    assert!(found_handler.is_some(), "应该能找到支持Request类型的处理器");
}

/// 测试主机注册功能
#[tokio::test]
async fn test_host_registration() {
    let registration = registry::HostRegistration::new(
        "com.test.secure_password".to_string(),
        "Test Secure Password Manager".to_string(),
        "/test/path/to/host".to_string(),
        vec!["chrome-extension://test-id/".to_string()],
    );
    
    // 验证注册信息
    assert_eq!(registration.name, "com.test.secure_password");
    assert_eq!(registration.description, "Test Secure Password Manager");
    assert_eq!(registration.path, "/test/path/to/host");
    assert_eq!(registration.allowed_origins.len(), 1);
    
    // 测试注册管理器
    let mut registry_manager = registry::RegistryManager::new("/test/daemon/path".to_string());
    registry_manager.create_registrations(vec!["test-extension".to_string()]).unwrap();
    
    // 验证注册配置创建
    let chrome_registration = registry_manager.get_registration(&browser::BrowserType::Chrome);
    assert!(chrome_registration.is_some());
    
    let firefox_registration = registry_manager.get_registration(&browser::BrowserType::Firefox);
    assert!(firefox_registration.is_some());
}

/// 测试错误处理集成
#[tokio::test]
async fn test_error_handling_integration() {
    // 测试协议错误
    let protocol_error = NativeMessagingError::ProtocolError("测试协议错误".to_string());
    let error_message = format!("{}", protocol_error);
    assert!(error_message.contains("协议错误"));
    
    // 测试序列化错误
    let serialization_error = NativeMessagingError::SerializationError("测试序列化错误".to_string());
    let error_message = format!("{}", serialization_error);
    assert!(error_message.contains("序列化错误"));
    
    // 测试安全错误
    let security_error = NativeMessagingError::SecurityError("测试安全错误".to_string());
    let error_message = format!("{}", security_error);
    assert!(error_message.contains("安全错误"));
}

/// 测试并发处理
#[tokio::test]
async fn test_concurrent_processing() {
    let codec = protocol::ProtocolCodec::new(protocol::ProtocolVersion::V2);
    let mut handles = vec![];
    
    // 创建多个并发任务
    for i in 0..10 {
        let codec_clone = codec.clone();
        let handle = tokio::spawn(async move {
            let message = NativeMessage::new_request(
                format!("concurrent_request_{}", i),
                json!({"test": "data", "index": i}),
                "test_extension".to_string(),
            );
            
            // 编码和解码
            let encoded = codec_clone.encode(&message).expect("编码失败");
            let decoded = codec_clone.decode(&encoded).expect("解码失败");
            
            assert_eq!(decoded.request_id, message.request_id);
            assert_eq!(decoded.payload, message.payload);
        });
        
        handles.push(handle);
    }
    
    // 等待所有任务完成
    for handle in handles {
        handle.await.expect("任务执行失败");
    }
}

/// 测试端到端流程
#[tokio::test]
async fn test_end_to_end_flow() {
    // 创建主机配置
    let host_config = test_helpers::create_test_host_config();
    
    // 创建代理配置
    let proxy_config = test_helpers::create_test_proxy_config();
    let ipc_client = test_helpers::create_test_ipc_client();
    
    // 创建请求代理
    let proxy = RequestProxy::new(proxy_config, ipc_client).await.expect("创建代理失败");
    
    // 创建浏览器适配器
    let chrome_config = browser::BrowserAdapterConfig::default();
    let chrome_adapter = browser::ChromeAdapter::new(chrome_config);
    
    // 创建消息处理器
    let mut handler_registry = handlers::HandlerRegistry::new();
    let default_handler = handlers::DefaultMessageHandler::new();
    handler_registry.register_handler("default".to_string(), Box::new(default_handler));
    
    // 创建测试消息
    let test_message = test_helpers::create_test_request_message();
    
    // 验证消息
    assert!(test_message.validate().is_ok());
    
    // 测试浏览器适配器处理消息
    let response = chrome_adapter.handle_message(test_message.clone()).await;
    assert!(response.is_ok());
    
    let response_message = response.unwrap();
    assert_eq!(response_message.request_id, test_message.request_id);
    assert_eq!(response_message.get_message_type(), MessageType::Response);
}

/// 测试性能基准
#[tokio::test]
async fn test_performance_benchmarks() {
    let codec = protocol::ProtocolCodec::new(protocol::ProtocolVersion::V2);
    let test_message = test_helpers::create_test_request_message();
    
    let start_time = std::time::Instant::now();
    
    // 执行1000次编解码操作
    for _ in 0..1000 {
        let encoded = codec.encode(&test_message).expect("编码失败");
        let _decoded = codec.decode(&encoded).expect("解码失败");
    }
    
    let elapsed = start_time.elapsed();
    println!("1000次编解码操作耗时: {:?}", elapsed);
    
    // 验证性能要求 (应该在1秒内完成)
    assert!(elapsed < Duration::from_secs(1), "性能测试未通过");
}

/// 测试内存管理
#[tokio::test]
async fn test_memory_management() {
    let codec = protocol::ProtocolCodec::new(protocol::ProtocolVersion::V2);
    
    // 创建大量消息进行测试
    let mut messages = Vec::new();
    for i in 0..1000 {
        let message = NativeMessage::new_request(
            format!("memory_test_{}", i),
            json!({"data": format!("test_data_{}", i)}),
            "test_extension".to_string(),
        );
        messages.push(message);
    }
    
    // 编码所有消息
    let mut encoded_messages = Vec::new();
    for message in &messages {
        let encoded = codec.encode(message).expect("编码失败");
        encoded_messages.push(encoded);
    }
    
    // 解码所有消息
    let mut decoded_messages = Vec::new();
    for encoded in &encoded_messages {
        let decoded = codec.decode(encoded).expect("解码失败");
        decoded_messages.push(decoded);
    }
    
    // 验证解码结果
    for (original, decoded) in messages.iter().zip(decoded_messages.iter()) {
        assert_eq!(original.request_id, decoded.request_id);
        assert_eq!(original.payload, decoded.payload);
    }
}

/// 测试错误恢复
#[tokio::test]
async fn test_error_recovery() {
    let mut message = test_helpers::create_test_request_message();
    
    // 模拟消息损坏
    message.request_id = String::new();
    
    // 验证错误检测
    assert!(message.validate().is_err());
    
    // 修复消息
    message.request_id = "recovered_request".to_string();
    
    // 验证恢复后的消息
    assert!(message.validate().is_ok());
    assert_eq!(message.request_id, "recovered_request");
} 