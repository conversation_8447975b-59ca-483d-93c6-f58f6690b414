//! 简单的浏览器适配层测试

use secure_password_daemon::native_messaging::browser::{BrowserType, BrowserAdapterConfig, ChromeAdapter, FirefoxAdapter};
use secure_password_daemon::native_messaging::protocol::NativeMessage;

#[tokio::test]
async fn test_browser_types() {
    // 测试浏览器类型的基本功能
    assert_eq!(BrowserType::Chrome.name(), "Chrome");
    assert_eq!(BrowserType::Firefox.name(), "Firefox");
    assert_eq!(BrowserType::Edge.name(), "Edge");
    assert_eq!(BrowserType::Safari.name(), "Safari");
    
    assert_eq!(BrowserType::Chrome.host_name(), "com.securepassword.chrome");
    assert_eq!(BrowserType::Firefox.host_name(), "com.securepassword.firefox");
}

#[tokio::test]
async fn test_browser_adapter_config() {
    let config = BrowserAdapterConfig::default();
    assert_eq!(config.browser_type, BrowserType::Chrome);
    assert_eq!(config.connection_timeout, 30);
    assert_eq!(config.max_retries, 3);
    assert!(!config.debug_mode);
    assert!(config.allowed_extensions.is_empty());
}

#[tokio::test]
async fn test_chrome_adapter_creation() {
    let config = BrowserAdapterConfig::default();
    let adapter = ChromeAdapter::new(config);
    
    assert_eq!(adapter.browser_type(), BrowserType::Chrome);
    
    let stats = adapter.stats().await;
    assert_eq!(stats.total_messages, 0);
    assert_eq!(stats.successful_messages, 0);
    assert_eq!(stats.success_rate(), 0.0);
}

#[tokio::test]
async fn test_firefox_adapter_creation() {
    let config = BrowserAdapterConfig::default();
    let adapter = FirefoxAdapter::new(config);
    
    assert_eq!(adapter.browser_type(), BrowserType::Firefox);
    
    let stats = adapter.stats().await;
    assert_eq!(stats.total_messages, 0);
    assert_eq!(stats.successful_messages, 0);
    assert_eq!(stats.success_rate(), 0.0);
}

#[tokio::test]
async fn test_native_message_creation() {
    let message = NativeMessage::new_request(
        "test-123".to_string(),
        serde_json::json!({"action": "test", "data": "hello"}),
        "test-extension".to_string(),
    );
    
    assert_eq!(message.request_id, "test-123");
    assert_eq!(message.source, "test-extension");
    assert_eq!(message.message_type, "request");
    
    // 测试消息验证
    assert!(message.validate().is_ok());
}

#[tokio::test]
async fn test_ping_message() {
    let ping = NativeMessage::new_ping("test-extension".to_string());
    
    assert_eq!(ping.source, "test-extension");
    assert_eq!(ping.message_type, "ping");
    assert!(ping.validate().is_ok());
}

#[tokio::test]
async fn test_extension_validation() {
    let mut config = BrowserAdapterConfig::default();
    config.allowed_extensions = vec!["allowed-extension".to_string()];
    
    let adapter = ChromeAdapter::new(config);
    
    // 测试允许的扩展
    assert!(adapter.validate_extension("allowed-extension").is_ok());
    
    // 测试不允许的扩展
    assert!(adapter.validate_extension("forbidden-extension").is_err());
}

#[tokio::test]
async fn test_extension_validation_empty_whitelist() {
    let config = BrowserAdapterConfig::default(); // 空的允许列表
    let adapter = ChromeAdapter::new(config);
    
    // 当允许列表为空时，应该允许所有扩展
    assert!(adapter.validate_extension("any-extension").is_ok());
}

#[test]
fn test_browser_type_display() {
    assert_eq!(format!("{:?}", BrowserType::Chrome), "Chrome");
    assert_eq!(format!("{:?}", BrowserType::Firefox), "Firefox");
    assert_eq!(format!("{:?}", BrowserType::Edge), "Edge");
    assert_eq!(format!("{:?}", BrowserType::Safari), "Safari");
}

#[test]
fn test_browser_adapter_config_clone() {
    let config1 = BrowserAdapterConfig::default();
    let config2 = config1.clone();
    
    assert_eq!(config1.browser_type, config2.browser_type);
    assert_eq!(config1.connection_timeout, config2.connection_timeout);
    assert_eq!(config1.max_retries, config2.max_retries);
}

#[tokio::test]
async fn test_adapter_stats_update() {
    let config = BrowserAdapterConfig::default();
    let adapter = ChromeAdapter::new(config);
    
    // 初始统计
    let initial_stats = adapter.stats().await;
    assert_eq!(initial_stats.total_messages, 0);
    
    // 创建一个测试消息来触发统计更新
    let message = NativeMessage::new_request(
        "stats-test".to_string(),
        serde_json::json!({"action": "test"}),
        "test-extension".to_string(),
    );
    
    // 处理消息（这会更新统计信息）
    let _response = adapter.handle_message(message).await.unwrap();
    
    // 检查统计信息是否更新
    let updated_stats = adapter.stats().await;
    assert_eq!(updated_stats.total_messages, 1);
    assert_eq!(updated_stats.successful_messages, 1);
    assert_eq!(updated_stats.success_rate(), 1.0);
} 