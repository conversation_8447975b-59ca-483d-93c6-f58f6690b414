//! 浏览器适配层集成测试

use secure_password_daemon::native_messaging::{
    browser::{BrowserType, BrowserAdapterConfig, ChromeAdapter, FirefoxAdapter, BrowserRegistry},
    protocol::NativeMessage,
};

#[tokio::test]
async fn test_browser_type_methods() {
    // 测试浏览器类型的基本方法
    assert_eq!(BrowserType::Chrome.name(), "Chrome");
    assert_eq!(BrowserType::Firefox.name(), "Firefox");
    assert_eq!(BrowserType::Edge.name(), "Edge");
    assert_eq!(BrowserType::Safari.name(), "Safari");
    
    // 测试主机名
    assert_eq!(BrowserType::Chrome.host_name(), "com.securepassword.chrome");
    assert_eq!(BrowserType::Firefox.host_name(), "com.securepassword.firefox");
}

#[tokio::test]
async fn test_browser_adapter_config() {
    let config = BrowserAdapterConfig::default();
    assert_eq!(config.browser_type, BrowserType::Chrome);
    assert_eq!(config.connection_timeout, 30);
    assert_eq!(config.max_retries, 3);
    assert!(!config.debug_mode);
    assert!(config.allowed_extensions.is_empty());
}

#[tokio::test]
async fn test_chrome_adapter() {
    let config = BrowserAdapterConfig::default();
    let adapter = ChromeAdapter::new(config);
    
    assert_eq!(adapter.browser_type(), BrowserType::Chrome);
    
    // 测试统计信息
    let stats = adapter.stats().await;
    assert_eq!(stats.total_messages, 0);
    assert_eq!(stats.successful_messages, 0);
    assert_eq!(stats.success_rate(), 0.0);
}

#[tokio::test]
async fn test_firefox_adapter() {
    let config = BrowserAdapterConfig::default();
    let adapter = FirefoxAdapter::new(config);
    
    assert_eq!(adapter.browser_type(), BrowserType::Firefox);
    
    // 测试统计信息
    let stats = adapter.stats().await;
    assert_eq!(stats.total_messages, 0);
    assert_eq!(stats.successful_messages, 0);
    assert_eq!(stats.success_rate(), 0.0);
}

#[tokio::test]
async fn test_chrome_adapter_message_handling() {
    let config = BrowserAdapterConfig::default();
    let adapter = ChromeAdapter::new(config);
    
    // 创建测试消息
    let message = NativeMessage::new_request(
        "test-request-123".to_string(),
        serde_json::json!({"action": "test", "data": "hello"}),
        "test-extension".to_string(),
    );
    
    // 处理消息
    let response = adapter.handle_message(message).await.unwrap();
    
    // 验证响应
    assert_eq!(response.request_id, "test-request-123");
    assert_eq!(response.source, "daemon");
    assert_eq!(response.get_message_type(), secure_password_daemon::native_messaging::protocol::MessageType::Response);
    
    // 检查统计信息是否更新
    let stats = adapter.stats().await;
    assert_eq!(stats.total_messages, 1);
    assert_eq!(stats.successful_messages, 1);
    assert_eq!(stats.success_rate(), 1.0);
}

#[tokio::test]
async fn test_firefox_adapter_message_handling() {
    let config = BrowserAdapterConfig::default();
    let adapter = FirefoxAdapter::new(config);
    
    // 创建测试消息
    let message = NativeMessage::new_request(
        "test-request-456".to_string(),
        serde_json::json!({"action": "test", "data": "world"}),
        "test-extension".to_string(),
    );
    
    // 处理消息
    let response = adapter.handle_message(message).await.unwrap();
    
    // 验证响应
    assert_eq!(response.request_id, "test-request-456");
    assert_eq!(response.source, "daemon");
    assert_eq!(response.get_message_type(), secure_password_daemon::native_messaging::protocol::MessageType::Response);
    
    // 检查统计信息是否更新
    let stats = adapter.stats().await;
    assert_eq!(stats.total_messages, 1);
    assert_eq!(stats.successful_messages, 1);
    assert_eq!(stats.success_rate(), 1.0);
}

#[tokio::test]
async fn test_ping_message_handling() {
    let config = BrowserAdapterConfig::default();
    let adapter = ChromeAdapter::new(config);
    
    // 创建心跳消息
    let ping_message = NativeMessage::new_ping("test-extension".to_string());
    
    // 处理心跳消息
    let response = adapter.handle_message(ping_message.clone()).await.unwrap();
    
    // 验证响应是pong
    assert_eq!(response.request_id, ping_message.request_id);
    assert_eq!(response.source, "daemon");
    assert_eq!(response.get_message_type(), secure_password_daemon::native_messaging::protocol::MessageType::Pong);
}

#[tokio::test]
async fn test_browser_registry() {
    let registry = BrowserRegistry::default();
    
    // 检查支持的浏览器
    let supported = registry.supported_browsers();
    assert!(supported.contains(&BrowserType::Chrome));
    assert!(supported.contains(&BrowserType::Firefox));
    assert_eq!(supported.len(), 2);
    
    // 测试获取适配器
    let chrome_adapter = registry.get_adapter(&BrowserType::Chrome);
    assert!(chrome_adapter.is_some());
    
    let firefox_adapter = registry.get_adapter(&BrowserType::Firefox);
    assert!(firefox_adapter.is_some());
    
    let edge_adapter = registry.get_adapter(&BrowserType::Edge);
    assert!(edge_adapter.is_none()); // 默认注册表中没有Edge
}

#[tokio::test]
async fn test_browser_registry_message_handling() {
    let registry = BrowserRegistry::default();
    
    // 创建测试消息
    let message = NativeMessage::new_request(
        "registry-test".to_string(),
        serde_json::json!({"test": "registry"}),
        "test-extension".to_string(),
    );
    
    // 通过注册表处理Chrome消息
    let response = registry.handle_browser_message(&BrowserType::Chrome, message.clone()).await.unwrap();
    assert_eq!(response.request_id, "registry-test");
    assert_eq!(response.source, "daemon");
    
    // 通过注册表处理Firefox消息
    let response = registry.handle_browser_message(&BrowserType::Firefox, message).await.unwrap();
    assert_eq!(response.request_id, "registry-test");
    assert_eq!(response.source, "daemon");
}

#[tokio::test]
async fn test_browser_registry_unsupported_browser() {
    let registry = BrowserRegistry::default();
    
    let message = NativeMessage::new_request(
        "unsupported-test".to_string(),
        serde_json::json!({"test": "unsupported"}),
        "test-extension".to_string(),
    );
    
    // 尝试处理不支持的浏览器消息
    let result = registry.handle_browser_message(&BrowserType::Edge, message).await;
    assert!(result.is_err());
}

#[tokio::test]
async fn test_extension_validation() {
    let mut config = BrowserAdapterConfig::default();
    config.allowed_extensions = vec!["allowed-extension".to_string()];
    
    let adapter = ChromeAdapter::new(config);
    
    // 测试允许的扩展
    let result = adapter.validate_extension("allowed-extension");
    assert!(result.is_ok());
    
    // 测试不允许的扩展
    let result = adapter.validate_extension("forbidden-extension");
    assert!(result.is_err());
}

#[tokio::test]
async fn test_extension_validation_empty_whitelist() {
    let config = BrowserAdapterConfig::default(); // 空的允许列表
    let adapter = ChromeAdapter::new(config);
    
    // 当允许列表为空时，应该允许所有扩展
    let result = adapter.validate_extension("any-extension");
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_browser_adapter_stats() {
    let config = BrowserAdapterConfig::default();
    let adapter = ChromeAdapter::new(config);
    
    // 初始统计信息
    let stats = adapter.stats().await;
    assert_eq!(stats.total_messages, 0);
    assert_eq!(stats.success_rate(), 0.0);
    
    // 处理几个消息
    for i in 0..3 {
        let message = NativeMessage::new_request(
            format!("test-{}", i),
            serde_json::json!({"counter": i}),
            "test-extension".to_string(),
        );
        
        let _response = adapter.handle_message(message).await.unwrap();
    }
    
    // 检查更新后的统计信息
    let stats = adapter.stats().await;
    assert_eq!(stats.total_messages, 3);
    assert_eq!(stats.successful_messages, 3);
    assert_eq!(stats.success_rate(), 1.0);
    assert!(stats.avg_response_time > 0); // 应该有一些响应时间
} 